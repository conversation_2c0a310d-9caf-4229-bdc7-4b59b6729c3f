'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import ThemeToggle from '@/components/ThemeToggle';
import axios from 'axios';

export default function VerifyEmailContent() {
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [message, setMessage] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const { login } = useAuthStore();

  useEffect(() => {
    if (token) {
      verifyEmail();
    } else {
      setVerificationStatus('error');
      setMessage('No verification token provided');
    }
  }, [token]);

  const verifyEmail = async () => {
    setIsVerifying(true);
    try {
      const response = await axios.post('/api/auth/verify-email', { token });
      
      if (response.data.success) {
        setVerificationStatus('success');
        setMessage('Email verified successfully! You can now log in.');
        
        // If user data is returned, log them in automatically
        if (response.data.user && response.data.token) {
          await login(response.data.user.email, '', true, response.data.user, response.data.token);
          setTimeout(() => {
            router.push('/dashboard');
          }, 2000);
        }
      } else {
        setVerificationStatus('error');
        setMessage(response.data.message || 'Verification failed');
      }
    } catch (error: unknown) {
      setVerificationStatus('error');
      if (axios.isAxiosError(error)) {
        setMessage(error.response?.data?.message || 'Verification failed');
      } else {
        setMessage('An unexpected error occurred');
      }
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendVerification = async () => {
    // Implementation for resending verification email
    try {
      const response = await axios.post('/api/auth/resend-verification');
      if (response.data.success) {
        setMessage('Verification email sent! Please check your inbox.');
      }
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        setMessage(error.response?.data?.message || 'Failed to resend verification email');
      } else {
        setMessage('An unexpected error occurred');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
          Email Verification
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card>
          <CardHeader>
            <CardTitle>Verify Your Email</CardTitle>
            <CardDescription>
              {verificationStatus === 'pending' && 'Verifying your email address...'}
              {verificationStatus === 'success' && 'Email verified successfully!'}
              {verificationStatus === 'error' && 'Verification failed'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isVerifying && (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            )}

            {message && (
              <div className={`p-4 rounded-md ${
                verificationStatus === 'success' 
                  ? 'bg-green-50 text-green-700 border border-green-200' 
                  : verificationStatus === 'error'
                  ? 'bg-red-50 text-red-700 border border-red-200'
                  : 'bg-blue-50 text-blue-700 border border-blue-200'
              }`}>
                {message}
              </div>
            )}

            <div className="space-y-4">
              {verificationStatus === 'success' && (
                <Button 
                  onClick={() => router.push('/login')}
                  className="w-full"
                >
                  Go to Login
                </Button>
              )}

              {verificationStatus === 'error' && (
                <div className="space-y-2">
                  <Button 
                    onClick={handleResendVerification}
                    variant="outline"
                    className="w-full"
                  >
                    Resend Verification Email
                  </Button>
                  <Button 
                    onClick={() => router.push('/login')}
                    className="w-full"
                  >
                    Back to Login
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
