'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import ThemeToggle from '@/components/ThemeToggle';
import axios from 'axios';

export default function VerifyEmailChangeContent() {
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [message, setMessage] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const { user, updateUser } = useAuthStore();

  useEffect(() => {
    if (token) {
      verifyEmailChange();
    } else {
      setVerificationStatus('error');
      setMessage('No verification token provided');
    }
  }, [token]);

  const verifyEmailChange = async () => {
    setIsVerifying(true);
    try {
      const response = await axios.post('/api/auth/verify-email-change', { token });
      
      if (response.data.success) {
        setVerificationStatus('success');
        setMessage('Email address updated successfully!');
        
        // Update user data if provided
        if (response.data.user) {
          updateUser(response.data.user);
        }
      } else {
        setVerificationStatus('error');
        setMessage(response.data.message || 'Email verification failed');
      }
    } catch (error: unknown) {
      setVerificationStatus('error');
      if (axios.isAxiosError(error)) {
        setMessage(error.response?.data?.message || 'Email verification failed');
      } else {
        setMessage('An unexpected error occurred');
      }
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
          Email Change Verification
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card>
          <CardHeader>
            <CardTitle>Verify Email Change</CardTitle>
            <CardDescription>
              {verificationStatus === 'pending' && 'Verifying your new email address...'}
              {verificationStatus === 'success' && 'Email change verified successfully!'}
              {verificationStatus === 'error' && 'Email change verification failed'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isVerifying && (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            )}

            {message && (
              <div className={`p-4 rounded-md ${
                verificationStatus === 'success' 
                  ? 'bg-green-50 text-green-700 border border-green-200' 
                  : verificationStatus === 'error'
                  ? 'bg-red-50 text-red-700 border border-red-200'
                  : 'bg-blue-50 text-blue-700 border border-blue-200'
              }`}>
                {message}
              </div>
            )}

            <div className="space-y-4">
              {verificationStatus === 'success' && (
                <Button 
                  onClick={() => router.push('/dashboard')}
                  className="w-full"
                >
                  Go to Dashboard
                </Button>
              )}

              {verificationStatus === 'error' && (
                <Button 
                  onClick={() => router.push('/dashboard')}
                  className="w-full"
                >
                  Back to Dashboard
                </Button>
              )}

              {verificationStatus === 'pending' && !isVerifying && (
                <Button 
                  onClick={() => router.push('/dashboard')}
                  variant="outline"
                  className="w-full"
                >
                  Cancel
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
