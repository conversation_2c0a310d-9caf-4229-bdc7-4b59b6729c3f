'use client';

import { Suspense } from 'react';
import NewWorkoutContent from './NewWorkoutContent';

// Force this page to be dynamic
export const dynamic = 'force-dynamic';

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <p className="mt-4 text-center text-gray-600 dark:text-gray-400">
          Loading workout page...
        </p>
      </div>
    </div>
  );
}

export default function NewWorkoutPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <NewWorkoutContent />
    </Suspense>
  );
}
