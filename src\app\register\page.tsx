'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import ThemeToggle from '@/components/ThemeToggle';

// Password Input Component with Visibility Toggle
interface PasswordInputProps {
  id: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
}

function PasswordInput({ id, placeholder, value, onChange, disabled, required }: PasswordInputProps) {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="relative">
      <Input
        id={id}
        type={showPassword ? "text" : "password"}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
        className="pr-10"
      />
      <button
        type="button"
        className="absolute inset-y-0 right-0 pr-3 flex items-center"
        onClick={() => setShowPassword(!showPassword)}
        disabled={disabled}
      >
        {showPassword ? (
          <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L8.464 8.464" />
          </svg>
        ) : (
          <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        )}
      </button>
    </div>
  );
}

export default function RegisterPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    numbers: false,
    uppercase: false,
    special: false
  });

  const { register, isAuthenticated, isLoading, error, requiresVerification, resendVerification, clearError } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    // Clear any existing errors when component mounts
    clearError();
    setValidationError('');
  }, [clearError]);

  // Real-time password validation
  useEffect(() => {
    const validation = {
      length: password.length >= 8,
      numbers: (password.match(/\d/g) || []).length >= 2,
      uppercase: /[A-Z]/.test(password),
      special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password)
    };
    setPasswordValidation(validation);
  }, [password]);

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    const numberCount = (password.match(/\d/g) || []).length;
    if (numberCount < 2) {
      return 'Password must contain at least 2 numbers';
    }

    if (!/[A-Z]/.test(password)) {
      return 'Password must contain at least 1 uppercase letter';
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password)) {
      return 'Password must contain at least 1 special character';
    }

    return '';
  };

  const validateForm = () => {
    const passwordValidationError = validatePassword(password);
    if (passwordValidationError) {
      setValidationError(passwordValidationError);
      return false;
    }

    if (password !== confirmPassword) {
      setValidationError('Passwords do not match');
      return false;
    }

    setValidationError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    const success = await register(email, password);
    if (success) {
      if (requiresVerification) {
        setRegistrationSuccess(true);
      } else {
        router.push('/dashboard');
      }
    }

    setIsSubmitting(false);
  };

  const handleResendVerification = async () => {
    setIsResending(true);
    await resendVerification(email);
    setIsResending(false);
  };

  if (isAuthenticated) {
    return null; // Will redirect
  }

  // Show verification success message
  if (registrationSuccess && requiresVerification) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="absolute top-4 right-4">
          <ThemeToggle />
        </div>

        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-success/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 7.89a1 1 0 001.42 0L21 7" />   
              </svg>
            </div>
            <CardTitle className="text-2xl text-center">Check Your Email!</CardTitle>
            <CardDescription className="text-center">
              We've sent a verification link to <strong>{email}</strong>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
              <p>Please check your email and click the verification link to activate your account.</p>
              <p className="mt-2">The verification link will expire in 24 hours.</p>
            </div>

            <div className="space-y-2">
              <Button
                onClick={handleResendVerification}
                disabled={isResending}
                variant="outline"
                className="w-full"
              >
                {isResending ? 'Sending...' : 'Resend Verification Email'}
              </Button>

              <Button
                onClick={() => router.push('/login')}
                className="w-full"
              >
                Back to Login
              </Button>
            </div>

            {error && (
              <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                {error}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl text-center">Create an account</CardTitle>
          <CardDescription className="text-center">
            Enter your information to get started
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                Email
              </label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading || isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">
                Password
              </label>
              <PasswordInput
                id="password"
                placeholder="Create a password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading || isSubmitting}
              />
              
              {/* Password Requirements */}
              <div className="text-xs space-y-1 mt-2">
                <p className="text-muted-foreground">Password must contain:</p>
                <div className="grid grid-cols-2 gap-1">
                  <div className={`flex items-center space-x-1 ${passwordValidation.length ? 'text-green-600' : 'text-muted-foreground'}`}>
                    <span className="text-xs">{passwordValidation.length ? '✓' : '○'}</span>
                    <span>8+ characters</span>
                  </div>
                  <div className={`flex items-center space-x-1 ${passwordValidation.numbers ? 'text-green-600' : 'text-muted-foreground'}`}>
                    <span className="text-xs">{passwordValidation.numbers ? '✓' : '○'}</span>
                    <span>2+ numbers</span>
                  </div>
                  <div className={`flex items-center space-x-1 ${passwordValidation.uppercase ? 'text-green-600' : 'text-muted-foreground'}`}>
                    <span className="text-xs">{passwordValidation.uppercase ? '✓' : '○'}</span>
                    <span>1+ uppercase</span>
                  </div>
                  <div className={`flex items-center space-x-1 ${passwordValidation.special ? 'text-green-600' : 'text-muted-foreground'}`}>
                    <span className="text-xs">{passwordValidation.special ? '✓' : '○'}</span>
                    <span>1+ special</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Special characters: ! @ # $ % ^ & * ( ) _ + - = [ ] { } ; ' : " \ | , . &lt; &gt; / ? ~ `
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="text-sm font-medium">
                Confirm Password
              </label>
              <PasswordInput
                id="confirmPassword"
                placeholder="Confirm your password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                disabled={isLoading || isSubmitting}
              />
              {confirmPassword && password !== confirmPassword && (
                <p className="text-xs text-red-600">Passwords do not match</p>
              )}
            </div>

            {(error || validationError) && (
              <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                {error || validationError}
              </div>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || isSubmitting || !email || !password || !confirmPassword}
            >
              {isLoading || isSubmitting ? 'Creating account...' : 'Create account'}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm">
            <span className="text-muted-foreground">Already have an account? </span>
            <Link
              href="/login"
              className="text-primary hover:underline font-medium"
            >
              Sign in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
