#!/bin/bash

# Fix unescaped quotes in JSX files
echo "Fixing unescaped quotes in JSX files..."

# Fix single quotes in JSX expressions
find src -name "*.tsx" -exec sed -i "s/}'/}&apos;/g" {} \;

# Fix double quotes in JSX expressions  
find src -name "*.tsx" -exec sed -i 's/}"/}&quot;/g' {} \;

# Fix standalone single quotes in JSX text
find src -name "*.tsx" -exec sed -i "s/Don't/Don&apos;t/g" {} \;
find src -name "*.tsx" -exec sed -i "s/can't/can&apos;t/g" {} \;
find src -name "*.tsx" -exec sed -i "s/won't/won&apos;t/g" {} \;
find src -name "*.tsx" -exec sed -i "s/doesn't/doesn&apos;t/g" {} \;
find src -name "*.tsx" -exec sed -i "s/isn't/isn&apos;t/g" {} \;
find src -name "*.tsx" -exec sed -i "s/haven't/haven&apos;t/g" {} \;

echo "Quote fixes completed!"
