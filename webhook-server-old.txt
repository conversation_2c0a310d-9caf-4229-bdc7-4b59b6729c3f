const http = require('http');
const crypto = require('crypto');
const { exec } = require('child_process');

// Your secret token (set this below and also in GitHub webhook settings)
const secret = 'shape_manip_lisener_2025!@#$';

function verifySignature(req, body) {
  const signature = req.headers['x-hub-signature-256'];
  if (!signature) return false;
  const hmac = crypto.createHmac('sha256', secret);
  const digest = 'sha256=' + hmac.update(body).digest('hex');
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest));
}

const server = http.createServer((req, res) => {
  if (req.method === 'POST' && req.url === '/webhook') {
    let body = '';

    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      if (!verifySignature(req, body)) {
        res.writeHead(401);
        return res.end('Invalid signature');
      }

      console.log('Webhook received, pulling latest changes...');

      exec('git -C C:/caddy/site pull origin main', (err, stdout, stderr) => {
        if (err) {
          console.error('Git pull error:', err);
          res.writeHead(500);
          return res.end('Git pull failed');
        }
        console.log(stdout);
        res.writeHead(200);
        res.end('Git pull successful');
      });
    });
  } else {
    res.writeHead(404);
    res.end();
  }
});

const PORT = 3001;
server.listen(PORT, () => console.log(`Webhook server listening on port ${PORT}`));
