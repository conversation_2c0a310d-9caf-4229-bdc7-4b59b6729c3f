<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>3D Art Studio - Interactive Shape Canvas</title>
    <link rel="stylesheet" href="styles.css">
    <script src="three.min.js"></script> <!-- Local three.js for faster loading -->
    <script src="app.js" defer></script> <!-- Reference external app.js file -->
</head>
<body>
    <div id="controls">
        <div>
            <label for="shape-select">Choose a shape:</label>
            <select id="shape-select">
                <option value="cube">Cube</option>
                <option value="sphere">Sphere</option>
                <option value="rectcube">RectCube</option>
                <option value="pyramid">Pyramid</option>
                <option value="cylinder">Cylinder</option>
                <option value="cone">Cone</option>
            </select>
        </div>
        <div>
            <label for="color-picker">Choose a color:</label>
            <div class="color-picker-container">
                <input type="color" id="color-picker">
                <div class="color-preview" id="color-preview"></div>
            </div>
        </div>
        <div>
            <button id="add-shape">Add Shape</button>
            <button id="reset-canvas">Reset Canvas</button>
        </div>
        <div>
            <label for="transparency-slider">Transparency:</label>
            <input type="range" id="transparency-slider" min="0.1" max="1" step="0.01" value="1">
        </div>
        <div class="control-buttons">
            <button id="camera-mode-btn">Camera Mode: OFF</button>
            <button id="shape-control-btn">Shape Control: ON</button>
        </div>
        <div class="mobile-controls" id="mobile-controls">
            <button id="delete-shape-btn" class="mobile-action-btn" disabled>🗑️ Delete</button>
            <button id="rotate-shape-btn" class="mobile-action-btn" disabled>🔄 Rotate</button>
            <button id="transparency-down-btn" class="mobile-action-btn" disabled>👻- Less Opaque</button>
            <button id="transparency-up-btn" class="mobile-action-btn" disabled>👻+ More Opaque</button>
        </div>
    </div>

    <div class="canvas-container">
        <canvas id="canvas"></canvas>
    </div>

    <div class="instructions">
        <strong>Desktop Controls:</strong><br>
        • Left click and press 'X' to delete selected shape<br>
        • Left click and hold 'Ctrl' to move shape closer/further<br>
        • Scroll wheel to zoom (camera mode) or resize shapes (shape mode)<br>
        • Right click and drag to rotate shapes<br><br>

        <strong>Touch Controls:</strong><br>
        • Tap to select shapes<br>
        • Drag with one finger to move shapes<br>
        • Double-tap a selected shape to toggle rotation mode<br>
        • In rotation mode: drag to rotate the shape<br>
        • Pinch with two fingers to zoom camera or scale shapes<br>
        • Use the mobile action buttons when a shape is selected<br>
        • Switch between Camera Mode and Shape Control using the top buttons
    </div>
</body>
</html>