const scene = new THREE.Scene();
const canvas = document.getElementById('canvas');
const camera = new THREE.PerspectiveCamera(75, 16/10, 0.1, 1000); // Use default 16:10 aspect ratio initially
const renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });

// Function to update canvas size and camera aspect ratio
function updateCanvasSize() {
    try {
        const container = canvas.parentElement;
        if (!container) {
            console.warn('Canvas container not found, retrying...');
            setTimeout(updateCanvasSize, 100);
            return;
        }

        const containerRect = container.getBoundingClientRect();

        // Calculate optimal canvas size based on container and viewport
        // Now we can use more space since buttons are in the nav
        const maxWidth = Math.min(containerRect.width * 0.98, window.innerWidth * 0.98);
        const maxHeight = Math.min(window.innerHeight * 0.85, containerRect.height * 0.95);

        // Ensure minimum size
        const minWidth = 400;
        const minHeight = 300;

        // Maintain a reasonable aspect ratio (16:10 or fit to screen)
        let canvasWidth, canvasHeight;
        const aspectRatio = 16 / 10;

        if (maxWidth / maxHeight > aspectRatio) {
            canvasHeight = Math.max(minHeight, maxHeight);
            canvasWidth = Math.max(minWidth, canvasHeight * aspectRatio);
        } else {
            canvasWidth = Math.max(minWidth, maxWidth);
            canvasHeight = Math.max(minHeight, canvasWidth / aspectRatio);
        }

        // Set canvas size with explicit pixel values
        canvas.style.width = Math.round(canvasWidth) + 'px';
        canvas.style.height = Math.round(canvasHeight) + 'px';

        // Set renderer size with device pixel ratio for crisp rendering
        const pixelRatio = window.devicePixelRatio || 1;
        renderer.setSize(Math.round(canvasWidth), Math.round(canvasHeight), false);
        renderer.setPixelRatio(pixelRatio);

        // Ensure canvas attributes match the actual size
        canvas.width = Math.round(canvasWidth * pixelRatio);
        canvas.height = Math.round(canvasHeight * pixelRatio);

        // Update camera aspect ratio
        camera.aspect = canvasWidth / canvasHeight;
        camera.updateProjectionMatrix();

        // Update line material resolution if using LineSegments2
        shapes.forEach(shape => {
            if (shape.userData.edgeLine && shape.userData.edgeLine.material && shape.userData.edgeLine.material.resolution) {
                shape.userData.edgeLine.material.resolution.set(canvasWidth * pixelRatio, canvasHeight * pixelRatio);
            }
        });

        requestRender();
        console.log(`Canvas resized to: ${canvasWidth}x${canvasHeight}`);
    } catch (error) {
        console.error('Error updating canvas size:', error);
    }
}

// Initial canvas setup
renderer.setClearColor(0xffffff, 1); // White background (not transparent)
camera.position.z = 5;

// Wait for DOM to be ready before sizing canvas
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', updateCanvasSize);
} else {
    updateCanvasSize();
}

const shapes = []; // Store shapes for interaction
const shapeMeshes = []; // Store only the mesh objects for precise hit detection
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();
let selectedShape = null;
let isRotating = false;
let lastMouseX = 0;
let lastMouseY = 0;
let cameraControlMode = false; // false = shape mode, true = camera mode
let cameraRotateMode = false;   // Controls camera orbit (rotation)
let cameraPanMode = false;      // Controls camera panning (location)
let cameraOrbitRadius = 5;
let cameraOrbitTheta = 0; // horizontal angle
let cameraOrbitPhi = Math.PI / 2; // vertical angle (PI/2 = level)
const cameraTarget = new THREE.Vector3(0, 0, 0);

// Get the camera control buttons from the navigation
const cameraModeBtn = document.getElementById('camera-mode-btn');
const shapeBtn = document.getElementById('shape-control-btn');

// Mobile control buttons
const deleteShapeBtn = document.getElementById('delete-shape-btn');
const rotateShapeBtn = document.getElementById('rotate-shape-btn');
const transparencyDownBtn = document.getElementById('transparency-down-btn');
const transparencyUpBtn = document.getElementById('transparency-up-btn');

// Optimized mobile button updates with state caching
let lastButtonState = null;

function updateMobileButtons() {
    const hasSelectedShape = selectedShape !== null || highlightedShape !== null;

    // Only update if state actually changed
    if (lastButtonState === hasSelectedShape) return;

    lastButtonState = hasSelectedShape;

    // Batch DOM updates
    requestAnimationFrame(() => {
        if (deleteShapeBtn) deleteShapeBtn.disabled = !hasSelectedShape;
        if (rotateShapeBtn) rotateShapeBtn.disabled = !hasSelectedShape;
        if (transparencyDownBtn) transparencyDownBtn.disabled = !hasSelectedShape;
        if (transparencyUpBtn) transparencyUpBtn.disabled = !hasSelectedShape;
    });
}

// Mobile button event listeners
if (deleteShapeBtn) {
    deleteShapeBtn.addEventListener('click', () => {
        const shapeToDelete = selectedShape || highlightedShape;
        if (shapeToDelete) {
            scene.remove(shapeToDelete);
            const idx = shapes.indexOf(shapeToDelete);
            if (idx !== -1) shapes.splice(idx, 1);
            const meshIdx = shapeMeshes.indexOf(shapeToDelete);
            if (meshIdx !== -1) shapeMeshes.splice(meshIdx, 1);

            if (highlightedShape === shapeToDelete) highlightShape(null);
            selectedShape = null;
            requestRender();
            updateMobileButtons();
        }
    });
}

if (rotateShapeBtn) {
    let rotationMode = false;
    let rotationInterval;

    rotateShapeBtn.addEventListener('click', () => {
        const currentShape = selectedShape || highlightedShape;
        if (!currentShape) return;

        rotationMode = !rotationMode;
        rotateShapeBtn.textContent = rotationMode ? '🔄 Stop Rotate' : '🔄 Rotate';
        rotateShapeBtn.style.background = rotationMode ?
            'linear-gradient(135deg, #28a745 0%, #20c997 100%)' :
            'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';

        if (rotationMode) {
            // Start continuous rotation using camera-relative axes
            rotationInterval = setInterval(() => {
                const shape = selectedShape || highlightedShape;
                if (shape) {
                    // Get camera's right and up vectors for camera-relative rotation
                    const cameraRight = new THREE.Vector3();
                    camera.getWorldDirection(cameraRight);
                    cameraRight.cross(camera.up).normalize();

                    const cameraUp = new THREE.Vector3();
                    cameraUp.copy(camera.up).normalize();

                    // Create rotation matrices for smooth continuous rotation
                    const rightRotation = new THREE.Matrix4().makeRotationAxis(cameraRight, 0.02);
                    const upRotation = new THREE.Matrix4().makeRotationAxis(cameraUp, 0.01);

                    // Apply rotations to the shape
                    shape.applyMatrix4(rightRotation);
                    shape.applyMatrix4(upRotation);

                    requestRender();
                } else {
                    // Stop rotation if no shape is selected
                    clearInterval(rotationInterval);
                    rotationMode = false;
                    rotateShapeBtn.textContent = '🔄 Rotate';
                    rotateShapeBtn.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';
                }
            }, 50); // 20fps rotation
        } else {
            // Stop rotation
            clearInterval(rotationInterval);
        }
    });
}

if (transparencyDownBtn) {
    transparencyDownBtn.addEventListener('click', () => {
        const shape = selectedShape || highlightedShape;
        if (shape) {
            let mat = shape.material;
            let edgeMat = shape.userData.edgeLine && shape.userData.edgeLine.material;
            let newOpacity = Math.max(0.1, (mat.opacity !== undefined ? mat.opacity : 1) - 0.1);

            if (mat) {
                mat.transparent = newOpacity < 1;
                mat.opacity = newOpacity;
            }
            if (edgeMat) {
                edgeMat.transparent = newOpacity < 1;
                edgeMat.opacity = newOpacity;
            }
            requestRender();
            updateTransparencySliderFromShape();
        }
    });
}

if (transparencyUpBtn) {
    transparencyUpBtn.addEventListener('click', () => {
        const shape = selectedShape || highlightedShape;
        if (shape) {
            let mat = shape.material;
            let edgeMat = shape.userData.edgeLine && shape.userData.edgeLine.material;
            let newOpacity = Math.min(1, (mat.opacity !== undefined ? mat.opacity : 1) + 0.1);

            if (mat) {
                mat.transparent = newOpacity < 1;
                mat.opacity = newOpacity;
            }
            if (edgeMat) {
                edgeMat.transparent = newOpacity < 1;
                edgeMat.opacity = newOpacity;
            }
            requestRender();
            updateTransparencySliderFromShape();
        }
    });
}

function updateButtonStates() {
    cameraModeBtn.textContent = cameraControlMode ? 'Camera Mode: ON' : 'Camera Mode: OFF';
    shapeBtn.textContent = cameraControlMode ? 'Shape Control: OFF' : 'Shape Control: ON';
}

cameraModeBtn.addEventListener('click', () => {
    cameraControlMode = !cameraControlMode;
    updateButtonStates();
    if (cameraControlMode) {
        highlightShape(null);
    }
});
shapeBtn.addEventListener('click', () => {
    cameraControlMode = false;
    updateButtonStates();
    // Optionally, highlight the last selected shape again
    highlightShape(selectedShape);
});

function updateCameraPosition() {
    // Clamp phi to avoid flipping
    cameraOrbitPhi = Math.max(0.01, Math.min(Math.PI - 0.01, cameraOrbitPhi));
    camera.position.x = cameraOrbitRadius * Math.sin(cameraOrbitPhi) * Math.sin(cameraOrbitTheta) + cameraTarget.x;
    camera.position.y = cameraOrbitRadius * Math.cos(cameraOrbitPhi) + cameraTarget.y;
    camera.position.z = cameraOrbitRadius * Math.sin(cameraOrbitPhi) * Math.cos(cameraOrbitTheta) + cameraTarget.z;
    camera.lookAt(cameraTarget);
}

// function addShape(shapeType, color = 0xeeeeee) { // Default color is now very light gray
function addShape(shapeType, color = 0xeeeeee) { // Default color is now very light gray
    try {
        let geometry;
        let edges; // <-- add this
        switch (shapeType) {
            case 'cube':
                geometry = new THREE.BoxGeometry(1, 1, 1);
                break;
            case 'rectcube':
                geometry = new THREE.BoxGeometry(2, 1, 0.5); // Rectangular cube
                break;
            case 'pyramid':
                geometry = new THREE.ConeGeometry(1, 2, 4);
                edges = new THREE.EdgesGeometry(geometry, 1); // thresholdAngle = 1 for sharp edges
                break;
            case 'cylinder':
                geometry = new THREE.CylinderGeometry(0.5, 0.5, 2, 32);
                break;
            case 'cone':
                geometry = new THREE.ConeGeometry(1, 2, 32);
                edges = new THREE.EdgesGeometry(geometry, 1); // thresholdAngle = 1 for sharp edges
                break;
            case 'sphere':
                geometry = new THREE.SphereGeometry(1, 32, 32);
                break;                
            default:
                console.error('Invalid shape type selected');
                return;
        }

        // Use MeshBasicMaterial for no shading
        const material = new THREE.MeshBasicMaterial({ color });
        const shape = new THREE.Mesh(geometry, material);

        // Add edges with increased thickness using LineSegments2/LineMaterial if available, else fallback to LineBasicMaterial
        let line;
        if (typeof THREE.LineSegments2 !== "undefined" && typeof THREE.LineMaterial !== "undefined") {
            // Use the edges variable if set, otherwise create normally
            const edgeGeometry = edges ? edges : new THREE.EdgesGeometry(geometry);
            const lineGeometry = new THREE.LineSegmentsGeometry().fromEdgesGeometry(edgeGeometry);
            const lineMaterial = new THREE.LineMaterial({
                color: 0x222222,
                linewidth: 0.008,
                dashed: false
            });
            // Set resolution based on actual canvas size
            const canvasRect = canvas.getBoundingClientRect();
            const pixelRatio = window.devicePixelRatio || 1;
            lineMaterial.resolution.set(canvasRect.width * pixelRatio, canvasRect.height * pixelRatio);
            line = new THREE.LineSegments2(lineGeometry, lineMaterial);
        } else {
            const edgeGeometry = edges ? edges : new THREE.EdgesGeometry(geometry);
            line = new THREE.LineSegments(
                edgeGeometry,
                new THREE.LineBasicMaterial({ color: 0x222222, linewidth: 6 })
            );
        }
        shape.add(line);
        shape.userData.edgeLine = line; // Store reference to edge line
        shape.userData.shapeType = shapeType; // Store shape type for rotation logic

        shape.position.set(0, 0, 0);
        scene.add(shape);
        shapes.push(shape); // Add shape to array for interaction
        shapeMeshes.push(shape); // Add mesh for precise hit detection
        requestRender(); // Ensure rendering updates

        // Debug: confirm shape creation
        console.log(`Added shape: ${shapeType}`);
    } catch (error) {
        console.error('Error adding shape:', error);
    }
}

const light = new THREE.PointLight(0xffffff, 1, 100);
light.position.set(10, 10, 10);
scene.add(light);

document.getElementById('add-shape').addEventListener('click', () => {
    try {
        const shapeSelect = document.getElementById('shape-select');
        const colorPicker = document.getElementById('color-picker');

        if (!shapeSelect || !colorPicker) {
            console.error('Required UI elements not found');
            return;
        }

        const shapeType = shapeSelect.value;
        const colorValue = colorPicker.value;

        // Validate inputs
        if (!shapeType) {
            console.error('No shape type selected');
            return;
        }

        const color = colorValue ? parseInt(colorValue.replace('#', '0x'), 16) : 0xeeeeee;

        // Validate color conversion
        if (isNaN(color)) {
            console.error('Invalid color value:', colorValue);
            return;
        }

        addShape(shapeType, color);
    } catch (error) {
        console.error('Error handling add-shape event:', error);
    }
});

document.getElementById('reset-canvas').addEventListener('click', () => {
    try {
        // Properly dispose of geometries and materials to prevent memory leaks
        shapes.forEach(shape => {
            if (shape.geometry) shape.geometry.dispose();
            if (shape.material) {
                if (Array.isArray(shape.material)) {
                    shape.material.forEach(mat => mat.dispose());
                } else {
                    shape.material.dispose();
                }
            }
            // Dispose edge line materials
            if (shape.userData.edgeLine) {
                if (shape.userData.edgeLine.geometry) shape.userData.edgeLine.geometry.dispose();
                if (shape.userData.edgeLine.material) shape.userData.edgeLine.material.dispose();
            }
            scene.remove(shape);
        });

        shapes.length = 0; // Clear shapes array
        shapeMeshes.length = 0; // Clear mesh array as well
        highlightShape(null); // Clear any highlights
        selectedShape = null;
        requestRender(); // Ensure rendering updates
    } catch (error) {
        console.error('Error handling reset-canvas event:', error);
    }
});

let resizingShape = null;
let isLeftMouseDown = false; // Track left mouse button state
let isMouseInCanvas = false; // Track if mouse is inside the canvas

// Touch support variables
let isTouchDevice = false;
let touchStartTime = 0;
let touchStartPos = { x: 0, y: 0 };
let lastTouchDistance = 0;
let isMultiTouch = false;
let touchMoveThreshold = 10; // pixels
let lastTouchPos = { x: 0, y: 0 };
let touchRotationMode = false;

let highlightedShape = null;
let originalMaterialColor = null;
let originalEdgeColor = null;

// Optimized highlight function with reduced material updates
function highlightShape(shape) {
    // Early return if trying to highlight the same shape
    if (highlightedShape === shape) return;

    // Remove highlight from previous shape
    if (highlightedShape && highlightedShape !== shape) {
        if (originalMaterialColor !== null && highlightedShape.material) {
            highlightedShape.material.color.set(originalMaterialColor);
            highlightedShape.material.needsUpdate = true;
        }
        if (highlightedShape.userData.edgeLine && originalEdgeColor !== null) {
            highlightedShape.userData.edgeLine.material.color.set(originalEdgeColor);
            highlightedShape.userData.edgeLine.material.needsUpdate = true;
        }
    }

    if (shape && shape.material) {
        // Store original colors only if not already stored
        if (highlightedShape !== shape) {
            originalMaterialColor = shape.material.color.getHex();
            if (shape.userData.edgeLine && shape.userData.edgeLine.material) {
                originalEdgeColor = shape.userData.edgeLine.material.color.getHex();
            }
        }

        // Set highlight color (yellow for mesh, orange for edge)
        shape.material.color.set(0xffff00);
        shape.material.needsUpdate = true;

        if (shape.userData.edgeLine && shape.userData.edgeLine.material) {
            shape.userData.edgeLine.material.color.set(0xff8800);
            shape.userData.edgeLine.material.needsUpdate = true;
        }

        highlightedShape = shape;
        updateTransparencySliderFromShape();
    } else {
        highlightedShape = null;
        originalMaterialColor = null;
        originalEdgeColor = null;
    }

    updateMobileButtons();
    requestRender(); // Single render request at the end
}

// Detect touch device
isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

// Debug function to check coordinate system health
function debugCoordinateSystem() {
    const canvasRect = canvas.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(canvas);

    console.log('=== COORDINATE SYSTEM DEBUG ===');
    console.log('Canvas element:', canvas);
    console.log('Canvas bounding rect:', canvasRect);
    console.log('Canvas width/height attributes:', canvas.width, canvas.height);
    console.log('Canvas style dimensions:', canvas.style.width, canvas.style.height);
    console.log('Computed style dimensions:', computedStyle.width, computedStyle.height);
    console.log('Device pixel ratio:', window.devicePixelRatio);
    console.log('Renderer size:', renderer.getSize(new THREE.Vector2()));
    console.log('Camera aspect:', camera.aspect);
    console.log('Window location:', window.location.href);
    console.log('User agent:', navigator.userAgent);
    console.log('================================');

    return {
        canvasRect,
        canvasAttributes: { width: canvas.width, height: canvas.height },
        canvasStyle: { width: canvas.style.width, height: canvas.style.height },
        computedStyle: { width: computedStyle.width, height: computedStyle.height },
        devicePixelRatio: window.devicePixelRatio,
        rendererSize: renderer.getSize(new THREE.Vector2()),
        cameraAspect: camera.aspect
    };
}

// Force recalibration function for server issues
function forceRecalibration() {
    console.log('Forcing coordinate system recalibration...');

    // Force canvas size update
    updateCanvasSize();

    // Wait a frame then update again
    requestAnimationFrame(() => {
        updateCanvasSize();
        console.log('Recalibration complete');
    });
}

// Performance monitoring
let performanceMetrics = {
    mouseDownTime: 0,
    highlightTime: 0,
    renderTime: 0,
    totalShapes: 0
};

function measurePerformance(operation, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    performanceMetrics[operation + 'Time'] = end - start;
    return result;
}

function getPerformanceReport() {
    performanceMetrics.totalShapes = shapes.length;
    console.log('=== PERFORMANCE METRICS ===');
    console.log('Mouse down time:', performanceMetrics.mouseDownTime.toFixed(2), 'ms');
    console.log('Highlight time:', performanceMetrics.highlightTime.toFixed(2), 'ms');
    console.log('Render time:', performanceMetrics.renderTime.toFixed(2), 'ms');
    console.log('Total shapes:', performanceMetrics.totalShapes);
    console.log('===========================');
    return performanceMetrics;
}

// Make debug functions globally available
window.debugCoordinateSystem = debugCoordinateSystem;
window.forceRecalibration = forceRecalibration;
window.getPerformanceReport = getPerformanceReport;

// Track mouse entering/leaving the canvas
canvas.addEventListener('mouseenter', () => { isMouseInCanvas = true; });
canvas.addEventListener('mouseleave', () => { isMouseInCanvas = false; });

// Touch event handlers
function getTouchPos(touch) {
    const canvasRect = canvas.getBoundingClientRect();
    return {
        x: touch.clientX - canvasRect.left,
        y: touch.clientY - canvasRect.top
    };
}

function updateMouseFromTouch(touch) {
    const canvasRect = canvas.getBoundingClientRect();
    const x = touch.clientX - canvasRect.left;
    const y = touch.clientY - canvasRect.top;

    mouse.x = (x / canvasRect.width) * 2 - 1;
    mouse.y = -(y / canvasRect.height) * 2 + 1;

    // Clamp values to prevent coordinate drift
    mouse.x = Math.max(-1, Math.min(1, mouse.x));
    mouse.y = Math.max(-1, Math.min(1, mouse.y));
}

function getTouchDistance(touch1, touch2) {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
}

canvas.addEventListener('touchstart', (event) => {
    event.preventDefault();
    isMouseInCanvas = true;

    const touches = event.touches;
    touchStartTime = Date.now();

    if (touches.length === 1) {
        // Single touch - treat like mouse down
        const touch = touches[0];
        const pos = getTouchPos(touch);
        touchStartPos = pos;
        lastTouchPos = pos; // Initialize for rotation tracking

        updateMouseFromTouch(touch);

        // Simulate mouse down event
        const mouseEvent = {
            button: 0,
            clientX: touch.clientX,
            clientY: touch.clientY
        };
        onMouseDown(mouseEvent);

    } else if (touches.length === 2) {
        // Two finger touch - for pinch zoom or camera control
        isMultiTouch = true;
        lastTouchDistance = getTouchDistance(touches[0], touches[1]);
    }
}, { passive: false });

canvas.addEventListener('touchmove', (event) => {
    event.preventDefault();

    const touches = event.touches;

    if (touches.length === 1 && !isMultiTouch) {
        // Single finger drag
        const touch = touches[0];
        updateMouseFromTouch(touch);

        if (selectedShape && !isRotating) {
            const currentPos = getTouchPos(touch);

            if (touchRotationMode) {
                // Rotation mode - use camera-relative rotation for intuitive touch control
                const deltaX = currentPos.x - lastTouchPos.x;
                const deltaY = currentPos.y - lastTouchPos.y;
                const rotationSpeed = 0.01;

                // Get camera's right and up vectors for camera-relative rotation
                const cameraRight = new THREE.Vector3();
                camera.getWorldDirection(cameraRight);
                cameraRight.cross(camera.up).normalize();

                const cameraUp = new THREE.Vector3();
                cameraUp.copy(camera.up).normalize();

                // Create rotation matrices for camera-relative axes
                const rightRotation = new THREE.Matrix4().makeRotationAxis(cameraRight, deltaY * rotationSpeed);
                const upRotation = new THREE.Matrix4().makeRotationAxis(cameraUp, deltaX * rotationSpeed);

                // Apply rotations to the shape
                selectedShape.applyMatrix4(rightRotation);
                selectedShape.applyMatrix4(upRotation);

                lastTouchPos = currentPos;
            } else {
                // Movement mode - use camera-relative movement for intuitive touch control
                const deltaX = (currentPos.x - touchStartPos.x);
                const deltaY = (currentPos.y - touchStartPos.y);
                const moveSpeed = 0.003 * cameraOrbitRadius || 0.015;

                // Move shape in camera's right and up directions (left/right/up/down)
                const right = new THREE.Vector3();
                camera.getWorldDirection(right);
                right.cross(camera.up).normalize();
                const up = new THREE.Vector3();
                up.copy(camera.up).normalize();
                selectedShape.position.addScaledVector(right, deltaX * moveSpeed * 0.1);
                selectedShape.position.addScaledVector(up, -deltaY * moveSpeed * 0.1);

                touchStartPos = currentPos;
            }

            requestRender();
        }

    } else if (touches.length === 2) {
        // Two finger gestures
        const currentDistance = getTouchDistance(touches[0], touches[1]);

        if (lastTouchDistance > 0) {
            const scale = currentDistance / lastTouchDistance;

            if (cameraControlMode) {
                // Pinch to zoom camera
                cameraOrbitRadius /= scale;
                cameraOrbitRadius = Math.max(1, Math.min(50, cameraOrbitRadius));
                updateCameraPosition();
                requestRender();
            } else if (selectedShape) {
                // Pinch to scale shape
                selectedShape.scale.multiplyScalar(scale);
                requestRender();
            }
        }

        lastTouchDistance = currentDistance;
    }
}, { passive: false });

// Double tap detection for rotation toggle
let lastTapTime = 0;
let tapCount = 0;

canvas.addEventListener('touchend', (event) => {
    event.preventDefault();

    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - touchStartTime;

    if (event.touches.length === 0) {
        // All fingers lifted
        isMultiTouch = false;
        lastTouchDistance = 0;

        // Double tap detection for rotation toggle
        if (touchDuration < 300) {
            const timeSinceLastTap = touchEndTime - lastTapTime;

            if (timeSinceLastTap < 500) { // Double tap within 500ms
                tapCount++;
                if (tapCount === 2 && selectedShape) {
                    // Double tap detected - toggle rotation mode
                    touchRotationMode = !touchRotationMode;
                    console.log('Touch rotation mode:', touchRotationMode ? 'ON' : 'OFF');
                    tapCount = 0;
                }
            } else {
                tapCount = 1;
            }
            lastTapTime = touchEndTime;
        }

        // Simulate mouse up
        onMouseUp({});
    }

    if (event.touches.length < 2) {
        isMultiTouch = false;
    }
}, { passive: false });

// Handle window resize
window.addEventListener('resize', () => {
    updateCanvasSize();
});

// Handle orientation change on mobile devices
window.addEventListener('orientationchange', () => {
    setTimeout(updateCanvasSize, 100);
});

function onMouseMove(event) {
    try {
        if (!isMouseInCanvas) return;

        // Get canvas bounding rectangle for accurate mouse coordinates
        const canvasRect = canvas.getBoundingClientRect();
        const x = event.clientX - canvasRect.left;
        const y = event.clientY - canvasRect.top;

        // More robust coordinate calculation that handles server differences
        mouse.x = (x / canvasRect.width) * 2 - 1;
        mouse.y = -(y / canvasRect.height) * 2 + 1;

        // Clamp to prevent coordinate system drift
        mouse.x = Math.max(-1, Math.min(1, mouse.x));
        mouse.y = Math.max(-1, Math.min(1, mouse.y));

        // Debug logging (disabled for performance)
        // Uncomment next line for coordinate debugging:
        // if (selectedShape && Math.random() < 0.001) console.log('Mouse coords:', mouse.x, mouse.y);

        // Shape movement is now handled in onMouseDrag for camera-relative movement
        // This prevents conflicts between world-space and camera-relative movement
    } catch (error) {
        console.error('Error handling mousemove event:', error);
    }
}

// Optimized shape selection with caching
let lastIntersectionCheck = 0;
let cachedIntersects = [];

function onMouseDown(event) {
    try {
        if (!isMouseInCanvas) return;
        if (event.button === 0) isLeftMouseDown = true;

        const now = performance.now();
        lastMouseX = event.clientX;
        lastMouseY = event.clientY;

        if (!cameraControlMode) {
            // Throttle raycasting to improve performance
            if (now - lastIntersectionCheck > 16) { // ~60fps limit
                raycaster.setFromCamera(mouse, camera);
                cachedIntersects = raycaster.intersectObjects(shapeMeshes, false);
                lastIntersectionCheck = now;
            }

            let newSelectedShape = null;

            if (cachedIntersects.length > 0) {
                let mesh = cachedIntersects[0].object;
                let obj = mesh;
                while (obj.parent && !shapes.includes(obj)) {
                    obj = obj.parent;
                }
                newSelectedShape = obj;
            } else {
                // Only check edge lines if no mesh intersection found
                const edgeLines = shapes.map(s => s.userData.edgeLine).filter(Boolean);
                if (edgeLines.length > 0) {
                    const edgeIntersects = raycaster.intersectObjects(edgeLines, false);
                    if (edgeIntersects.length > 0) {
                        let edgeObj = edgeIntersects[0].object;
                        let parentShape = shapes.find(s => s.userData.edgeLine === edgeObj);
                        if (parentShape) {
                            newSelectedShape = parentShape;
                        }
                    }
                }
            }

            // Only update if selection actually changed
            if (newSelectedShape !== selectedShape) {
                selectedShape = newSelectedShape;
                highlightShape(selectedShape);
            }

            if (selectedShape) {
                if (event.button === 0) {
                    isRotating = false;
                    resizingShape = selectedShape;
                } else if (event.button === 2) {
                    isRotating = true;
                }
            }
        }
        // else: camera modes handled in onMouseDrag
    } catch (error) {
        console.error('Error handling mousedown event:', error);
    }
}

function onMouseUp() {
    try {
        if (!isMouseInCanvas) return;
        isLeftMouseDown = false;
        selectedShape = null;
        isRotating = false;
        resizingShape = null;
        // Only remove highlight if there was one
        if (!cameraControlMode && highlightedShape !== null) {
            highlightShape(null);
        }
    } catch (error) {
        console.error('Error handling mouseup event:', error);
    }
}

function onMouseDrag(event) {
    try {
        if (!isMouseInCanvas) return;
        if (!cameraControlMode && selectedShape) {
            const dx = event.clientX - lastMouseX;
            const dy = event.clientY - lastMouseY;
            const moveSpeed = 0.003 * cameraOrbitRadius || 0.015; // Reduced sensitivity

            if (isRotating) {
                // Apply camera-relative rotation for intuitive control
                const rotationSpeed = 0.01;

                // Get camera's right and up vectors for camera-relative rotation
                const cameraRight = new THREE.Vector3();
                camera.getWorldDirection(cameraRight);
                cameraRight.cross(camera.up).normalize();

                const cameraUp = new THREE.Vector3();
                cameraUp.copy(camera.up).normalize();

                // Create rotation matrices for camera-relative axes
                const rightRotation = new THREE.Matrix4().makeRotationAxis(cameraRight, dy * rotationSpeed);
                const upRotation = new THREE.Matrix4().makeRotationAxis(cameraUp, dx * rotationSpeed);

                // Apply rotations to the shape
                selectedShape.applyMatrix4(rightRotation);
                selectedShape.applyMatrix4(upRotation);

                lastMouseX = event.clientX;
                lastMouseY = event.clientY;
            } else if (event.buttons === 1) {
                if (event.ctrlKey) {
                    // Move shape along camera's view direction (closer/further)
                    const cameraDir = new THREE.Vector3();
                    camera.getWorldDirection(cameraDir);
                    // Use dy for forward/backward (up is closer, down is further)
                    selectedShape.position.addScaledVector(cameraDir, -dy * moveSpeed);
                } else {
                    // Move shape in camera's right and up directions (left/right/up/down)
                    const right = new THREE.Vector3();
                    camera.getWorldDirection(right);
                    right.cross(camera.up).normalize();
                    const up = new THREE.Vector3();
                    up.copy(camera.up).normalize();
                    selectedShape.position.addScaledVector(right, dx * moveSpeed);
                    selectedShape.position.addScaledVector(up, -dy * moveSpeed);
                }
                lastMouseX = event.clientX;
                lastMouseY = event.clientY;
            }
            requestRender();
        } else if (cameraControlMode && event.buttons === 1) {
            // Camera pan mode (left mouse button)
            const dx = event.clientX - lastMouseX;
            const dy = event.clientY - lastMouseY;
            const panSpeed = 0.003 * cameraOrbitRadius;
            const right = new THREE.Vector3();
            camera.getWorldDirection(right);
            right.cross(camera.up).normalize();
            const up = new THREE.Vector3();
            up.copy(camera.up).normalize();
            cameraTarget.addScaledVector(right, -dx * panSpeed);
            cameraTarget.addScaledVector(up, dy * panSpeed);
            updateCameraPosition();
            lastMouseX = event.clientX;
            lastMouseY = event.clientY;
            requestRender();
        } else if (cameraControlMode && event.buttons === 2) {
            // Camera orbit (rotation) mode (right mouse button)
            const dx = event.clientX - lastMouseX;
            const dy = event.clientY - lastMouseY;
            cameraOrbitTheta -= dx * 0.01;
            const nextPhi = cameraOrbitPhi - dy * 0.01;
            cameraOrbitPhi = Math.max(0.01, Math.min(Math.PI - 0.01, nextPhi));
            updateCameraPosition();
            lastMouseX = event.clientX;
            lastMouseY = event.clientY;
            requestRender();
        }
    } catch (error) {
        console.error('Error handling mousemove drag event:', error);
    }
}

window.addEventListener('mousemove', onMouseMove);
window.addEventListener('mousedown', onMouseDown);
window.addEventListener('mouseup', onMouseUp);
window.addEventListener('mousemove', onMouseDrag);

window.addEventListener('wheel', (event) => {
    // Only handle wheel events when mouse is inside the canvas
    if (!isMouseInCanvas) return;

    if (cameraControlMode) {
        cameraOrbitRadius += event.deltaY * 0.01;
        cameraOrbitRadius = Math.max(1, cameraOrbitRadius);
        updateCameraPosition();
        requestRender();
        event.preventDefault();
    } else if (
        resizingShape
    ) {
        // Scale the selected shape
        let scaleDelta = event.deltaY < 0 ? 1.05 : 0.95;
        resizingShape.scale.multiplyScalar(scaleDelta);
        requestRender();
        // Prevent page scroll
        event.preventDefault();
    }
}, { passive: false });

window.addEventListener('contextmenu', (event) => event.preventDefault()); // Prevent default right-click menu

// Optimized animation loop - only render when needed
let needsRender = true;

function requestRender() {
    needsRender = true;
}

function animate() {
    try {
        requestAnimationFrame(animate);
        if (needsRender) {
            renderer.render(scene, camera);
            needsRender = false;
        }
    } catch (error) {
        console.error('Error during animation:', error);
    }
}

// Initialize camera position for orbit controls
updateCameraPosition();
animate();

// Set the default color for the color picker to #eeeeee and update preview
const colorPicker = document.getElementById('color-picker');
const colorPreview = document.getElementById('color-preview');

function updateColorPreview() {
    if (colorPicker && colorPreview) {
        const selectedColor = colorPicker.value;
        colorPreview.style.backgroundColor = selectedColor;

        // Add a subtle animation when color changes
        colorPreview.style.transform = 'scale(1.1)';
        setTimeout(() => {
            colorPreview.style.transform = 'scale(1)';
        }, 150);
    }
}

if (colorPicker) {
    colorPicker.value = '#eeeeee';
    updateColorPreview(); // Set initial preview

    // Update preview when color changes
    colorPicker.addEventListener('input', updateColorPreview);
    colorPicker.addEventListener('change', updateColorPreview);
}

function updateTransparencySliderFromShape() {
    const transparencySlider = document.getElementById('transparency-slider');
    const shape = highlightedShape || selectedShape;
    if (transparencySlider && shape && shape.material && typeof shape.material.opacity === 'number') {
        transparencySlider.value = shape.material.opacity;
    }
}

window.addEventListener('keydown', (event) => {
    // Only in shape mode, left mouse down, and a shape is selected
    if (
        !cameraControlMode &&
        isLeftMouseDown &&
        selectedShape
    ) {
        // Transparency adjustment
        if (event.key === 'q' || event.key === 'Q') {
            // Make more transparent
            let mat = selectedShape.material;
            let edgeMat = selectedShape.userData.edgeLine && selectedShape.userData.edgeLine.material;
            let newOpacity = Math.max(0.1, (mat.opacity !== undefined ? mat.opacity : 1) - 0.05);
            if (mat) {
                mat.transparent = newOpacity < 1;
                mat.opacity = newOpacity;
            }
            if (edgeMat) {
                edgeMat.transparent = newOpacity < 1;
                edgeMat.opacity = newOpacity;
            }
            requestRender();
            updateTransparencySliderFromShape();
        } else if (event.key === 'w' || event.key === 'W') {
            // Make less transparent
            let mat = selectedShape.material;
            let edgeMat = selectedShape.userData.edgeLine && selectedShape.userData.edgeLine.material;
            let newOpacity = Math.min(1, (mat.opacity !== undefined ? mat.opacity : 1) + 0.05);
            if (mat) {
                mat.transparent = newOpacity < 1;
                mat.opacity = newOpacity;
            }
            if (edgeMat) {
                edgeMat.transparent = newOpacity < 1;
                edgeMat.opacity = newOpacity;
            }
            requestRender();
            updateTransparencySliderFromShape();
        } else if (event.key === 'x' || event.key === 'X') {
            // Delete shape
            scene.remove(selectedShape);
            const idx = shapes.indexOf(selectedShape);
            if (idx !== -1) {
                shapes.splice(idx, 1);
            }
            // Remove from mesh array as well
            const meshIdx = shapeMeshes.indexOf(selectedShape);
            if (meshIdx !== -1) {
                shapeMeshes.splice(meshIdx, 1);
            }
            // Remove highlight if deleted
            if (highlightedShape === selectedShape) {
                highlightShape(null);
            }
            selectedShape = null;
            requestRender();
        }
    }
});



// Replace setAllShapesOpacity to only affect the highlighted/selected shape
function setSelectedShapeOpacity(opacity) {
    const shape = highlightedShape || selectedShape;
    if (shape && shape.material) {
        shape.material.transparent = opacity < 1;
        shape.material.opacity = opacity;
    }
    if (shape && shape.userData.edgeLine && shape.userData.edgeLine.material) {
        shape.userData.edgeLine.material.transparent = opacity < 1;
        shape.userData.edgeLine.material.opacity = opacity;
    }
    requestRender();
    updateTransparencySliderFromShape();
}

// Debounced transparency update for better performance
let transparencyUpdateTimeout;
function debouncedTransparencyUpdate(value) {
    clearTimeout(transparencyUpdateTimeout);
    transparencyUpdateTimeout = setTimeout(() => {
        setSelectedShapeOpacity(value);
    }, 16); // ~60fps
}

// After DOM is ready, add event listener for the transparency slider
window.addEventListener('DOMContentLoaded', () => {
    const transparencySlider = document.getElementById('transparency-slider');
    if (transparencySlider) {
        // Use input for real-time feedback, but debounced
        transparencySlider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value) && value >= 0.1 && value <= 1) {
                debouncedTransparencyUpdate(value);
            }
        });

        // Use change for final value
        transparencySlider.addEventListener('change', (e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value) && value >= 0.1 && value <= 1) {
                clearTimeout(transparencyUpdateTimeout);
                setSelectedShapeOpacity(value);
            }
        });
    }
});
