version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: exercise_tracker_backend_dev
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=file:./dev.db
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-change-this}
      - PORT=4000
    volumes:
      - ./backend/prisma/dev.db:/app/prisma/dev.db
      - ./backend/prisma:/app/prisma
    restart: unless-stopped
    networks:
      - exercise_tracker_network

  # Frontend Next.js Service (Development Mode)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend.dev
    container_name: exercise_tracker_frontend_dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://backend:4000
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - exercise_tracker_network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next

networks:
  exercise_tracker_network:
    driver: bridge

volumes:
  sqlite_data:
