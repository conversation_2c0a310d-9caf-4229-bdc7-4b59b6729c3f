const http = require('http');
const crypto = require('crypto');
const { exec } = require('child_process');

// Your secret token (set this below and also in GitHub webhook settings)
const secret = 'shape_manip_lisener_2025!@#$';

function verifySignature(req, body) {
  const signature = req.headers['x-hub-signature-256'];
  if (!signature) return false;

  const hmac = crypto.createHmac('sha256', secret);
  const digest = 'sha256=' + hmac.update(body).digest('hex');

  // Check if signatures have the same length before comparing
  if (signature.length !== digest.length) {
    console.log('Signature length mismatch:', { signature: signature.length, digest: digest.length });
    return false;
  }

  try {
    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest));
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

const server = http.createServer((req, res) => {
  try {
    if (req.method === 'POST' && req.url === '/webhook') {
      let body = '';

      req.on('data', chunk => body += chunk);
      req.on('end', () => {
        try {
          if (!verifySignature(req, body)) {
            console.log('Webhook signature verification failed');
            res.writeHead(401);
            return res.end('Invalid signature');
          }

          console.log('Webhook received, pulling latest changes...');

          exec('git -C C:/caddy/site pull origin main', (err, stdout, stderr) => {
            if (err) {
              console.error('Git pull error:', err);
              res.writeHead(500);
              return res.end('Git pull failed');
            }
            console.log('Git pull successful:', stdout);
            res.writeHead(200);
            res.end('Git pull successful');
          });
        } catch (error) {
          console.error('Error processing webhook:', error);
          res.writeHead(500);
          res.end('Internal server error');
        }
      });

      req.on('error', (error) => {
        console.error('Request error:', error);
        res.writeHead(400);
        res.end('Bad request');
      });
    } else {
      res.writeHead(404);
      res.end('Not found');
    }
  } catch (error) {
    console.error('Server error:', error);
    res.writeHead(500);
    res.end('Internal server error');
  }
});

const PORT = 3002; // Port for webhook server (proxied by Caddy)
server.listen(PORT, () => console.log(`Webhook server listening on port ${PORT}`));
