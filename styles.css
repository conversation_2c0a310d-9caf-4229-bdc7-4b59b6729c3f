* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

#controls {
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

#controls label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

#controls select, #controls input[type="color"], #controls button {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

#controls input[type="color"] {
    width: 50px;
    height: 40px;
    padding: 4px;
    cursor: pointer;
    background: none;
    border: 2px solid #ddd;
    border-radius: 8px;
}

#controls input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 6px;
}

#controls input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 6px;
    box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
}

#controls input[type="color"]::-moz-color-swatch {
    border: none;
    border-radius: 6px;
}

#controls input[type="color"]:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.color-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-preview {
    width: 40px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 8px;
    background-color: #eeeeee;
    box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.color-preview::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    color: rgba(0,0,0,0.5);
    pointer-events: none;
}

.control-buttons {
    display: flex;
    gap: 10px;
}

.control-buttons button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    font-size: 13px;
    padding: 8px 16px;
    min-width: 140px;
    text-align: center;
}

.control-buttons button:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

.mobile-controls {
    display: none; /* Hidden by default, shown on touch devices */
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-top: 10px;
}

.mobile-action-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
    text-align: center;
}

.mobile-action-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #ee5a52 0%, #dc3545 100%);
    transform: translateY(-1px);
}

.mobile-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Show mobile controls on touch devices */
@media (hover: none) and (pointer: coarse) {
    .mobile-controls {
        display: flex;
    }
}

#controls select:focus, #controls input:focus, #controls button:hover {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#controls button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

#controls input[type="range"] {
    accent-color: #667eea;
}

.canvas-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    min-height: calc(100vh - 180px); /* Adjusted for larger nav */
}

canvas {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    background: white; /* Solid white background */
    max-width: 95vw;
    max-height: 80vh;
    width: 100%;
    height: auto;
    display: block;
}

.instructions {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    margin: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-size: 15px;
    color: #555;
    line-height: 1.6;
}

/* Responsive design */
@media (max-width: 768px) {
    #controls {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    #controls > div {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
        justify-content: center;
    }

    .control-buttons {
        flex-direction: column;
        width: 100%;
    }

    .control-buttons button {
        width: 100%;
        min-width: auto;
    }

    .canvas-container {
        padding: 10px;
        min-height: calc(100vh - 250px); /* More space for expanded nav */
    }

    canvas {
        max-height: 65vh;
    }

    .instructions {
        margin: 10px;
        padding: 15px;
        font-size: 14px;
    }
}
